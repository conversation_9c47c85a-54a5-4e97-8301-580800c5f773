import React from 'react';
import './OurTeam.css';

interface TeamMember {
  id: number;
  name: string;
  position: string;
  bio: string;
  image: string;
  linkedin?: string;
}

const AboutUs: React.FC = () => {
  const teamMembers: TeamMember[] = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      position: "CEO & Co-Founder",
      bio: "Agricultural engineer with 15+ years in precision farming and IoT solutions.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "CTO & Co-Founder",
      bio: "Former Google engineer specializing in machine learning and agricultural data analytics.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 3,
      name: "Dr. <PERSON>",
      position: "Head of Research",
      bio: "PhD in Agricultural Science with expertise in sustainable farming practices.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 4,
      name: "James Park",
      position: "VP of Engineering",
      bio: "Full-stack developer with experience building scalable agricultural platforms.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 5,
      name: "Lisa Wang",
      position: "Head of Operations",
      bio: "Operations expert with 10+ years in supply chain management and logistics.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    }
  ];


  return (
    <div className="about-section">
      <div className="container">
        <div className="about-section__header">
          <h2 className="about-section__title">
            Meet the Team Behind AgriTram
          </h2>
          <p className="about-section__description">
            Our diverse team of agricultural experts, engineers, and data scientists
            is passionate about transforming farming through innovative technology solutions.
          </p>
        </div>

        <div className="team-grid">
          {teamMembers.map((member) => (
            <div key={member.id} className="team-card">
              <div className="team-card__image">
                <img 
                  src={member.image} 
                  alt={member.name}
                  loading="lazy"
                />
                {member.linkedin && (
                  <a 
                    href={member.linkedin} 
                    className="team-card__social"
                    aria-label={`${member.name} LinkedIn profile`}
                  >
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path 
                        d="M16.25 2.5H3.75C3.06 2.5 2.5 3.06 2.5 3.75V16.25C2.5 16.94 3.06 17.5 3.75 17.5H16.25C16.94 17.5 17.5 16.94 17.5 16.25V3.75C17.5 3.06 16.94 2.5 16.25 2.5ZM7.5 15H5V8.75H7.5V15ZM6.25 7.69C5.56 7.69 5 7.13 5 6.44C5 5.75 5.56 5.19 6.25 5.19C6.94 5.19 7.5 5.75 7.5 6.44C7.5 7.13 6.94 7.69 6.25 7.69ZM15 15H12.5V11.94C12.5 11.06 12.5 9.94 11.25 9.94C10 9.94 9.81 10.88 9.81 11.88V15H7.31V8.75H9.69V9.81H9.72C10.06 9.19 10.81 8.56 11.94 8.56C14.44 8.56 15 10.19 15 12.31V15Z" 
                        fill="currentColor"
                      />
                    </svg>
                  </a>
                )}
              </div>
              
              <div className="team-card__content">
                <h3 className="team-card__name">{member.name}</h3>
                <p className="team-card__position">{member.position}</p>
                <p className="team-card__bio">{member.bio}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
